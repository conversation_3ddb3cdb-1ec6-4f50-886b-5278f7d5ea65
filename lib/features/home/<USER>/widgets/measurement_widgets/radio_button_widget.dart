import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

class RadioButtonWidget extends StatelessWidget {
  final Measurement measurement;
  final String? value;
  final Function(String?) onChanged;
  final Widget? conditionalWidget; // Optional conditional widget to show

  const RadioButtonWidget({
    super.key,
    required this.measurement,
    required this.value,
    required this.onChanged,
    this.conditionalWidget,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final options = measurement.measurementOptions ?? [];

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black10,
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            measurement.measurementDescription ?? 'Select an option',
            style: textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              fontSize: 16,
              color: AppColors.black,
            ),
          ),
          const Gap(16),
          Row(
            children: options.map((option) {
              final optionId = option.measurementOptionId?.toString();
              final isSelected = value == optionId;

              return Expanded(
                child: GestureDetector(
                  onTap: () => onChanged(optionId),
                  child: Container(
                    margin: const EdgeInsets.only(right: 12.0),
                    child: Row(
                      children: [
                        Container(
                          width: 20,
                          height: 20,
                          decoration: BoxDecoration(
                            color: isSelected
                                ? AppColors.primaryBlue
                                : Colors.white,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: isSelected
                                  ? AppColors.primaryBlue
                                  : AppColors.blackTint2,
                              width: 2,
                            ),
                          ),
                          child: isSelected
                              ? Container(
                                  width: 8,
                                  height: 8,
                                  margin: const EdgeInsets.all(4),
                                  decoration: const BoxDecoration(
                                    color: Colors.white,
                                    shape: BoxShape.circle,
                                  ),
                                )
                              : null,
                        ),
                        const Gap(8),
                        Expanded(
                          child: Text(
                            option.measurementOptionDescription ?? 'Option',
                            style: textTheme.bodyMedium?.copyWith(
                              color: AppColors.black,
                              fontSize: 15,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
          if (conditionalWidget != null) ...[
            const Gap(16),
            conditionalWidget!,
          ],
          if (measurement.required == true && value == null)
            Padding(
              padding: const EdgeInsets.only(top: 12.0),
              child: Text(
                'This field is required',
                style: textTheme.bodySmall?.copyWith(
                  color: Colors.red,
                  fontSize: 12,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
